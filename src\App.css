/* Simple, Clean CSS for Quizzcasso */

:root {
  --gold: #d4af37;
  --silver: #c0c0c0;
  --light-silver: #e6e6e6;
  --emerald: #50c878;
  --deep-blue: #4169e1;
  --burgundy: #8b0000;
  --light-gold: #ffd700;
  --font-heading: '<PERSON>in<PERSON>', 'Times New Roman', serif;
  --font-main: 'Times New Roman', serif;
  --card-gradient: rgba(0, 0, 0, 0.4);
  --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --magical-shadow: 0 15px 45px rgba(212, 175, 55, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  text-transform: none !important;
  font-variant: normal !important;
}

body {
  font-family: 'Times New Roman', serif;
  background: linear-gradient(135deg, #0A0A1A 0%, #1A0B3D 50%, #2D1B69 100%);
  background-attachment: fixed;
  color: #E8E8E8;
  line-height: 1.6;
  min-height: 100vh;
  text-transform: none;
  font-variant: normal;
}

.App {
  min-height: 100vh;
}

/* Additional app-specific styles */
.page-container {
  min-height: 100vh;
  padding: 2rem 0;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Quiz specific styles */
.quiz-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.question-card {
  background: var(--card-gradient);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 15px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: var(--card-shadow);
}

.question-text {
  font-family: var(--font-main);
  font-size: 1.3rem;
  color: var(--deep-blue);
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.options-container {
  display: grid;
  gap: 1rem;
  margin: 1.5rem 0;
}

.option-button {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 10px;
  padding: 1rem;
  font-family: var(--font-main);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.option-button:hover {
  background: rgba(212, 175, 55, 0.1);
  border-color: var(--gold);
  transform: translateX(5px);
}

.option-button.selected {
  background: rgba(212, 175, 55, 0.2);
  border-color: var(--gold);
  font-weight: 600;
}

.quiz-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.quiz-progress {
  font-family: var(--font-heading);
  color: var(--gold);
  font-size: 1.1rem;
}

/* Team member cards */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.team-member-card {
  background: var(--card-gradient);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.team-member-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--magical-shadow);
}

.member-photo {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--gold);
  margin-bottom: 1rem;
}

.member-name {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  color: var(--gold);
  margin-bottom: 0.5rem;
}

.member-role {
  font-family: var(--font-main);
  font-style: italic;
  color: var(--emerald);
  margin-bottom: 1rem;
}

/* Leaderboard styles */
.leaderboard-table {
  width: 100%;
  background: rgba(15, 15, 35, 0.8);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 2px solid rgba(255, 215, 0, 0.2);
}

.leaderboard-table th,
.leaderboard-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
  color: var(--light-silver);
}

.leaderboard-table th {
  background: rgba(255, 215, 0, 0.1);
  color: var(--light-gold);
  font-family: var(--font-heading);
  font-weight: 600;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

.leaderboard-table tr:hover {
  background: rgba(255, 215, 0, 0.05);
}

.leaderboard-table tbody tr {
  background: rgba(15, 15, 35, 0.6);
}

/* Knowledge Subject Buttons */
.knowledge-subject-button {
  background: linear-gradient(45deg, var(--gold), #FFD700);
  border: 2px solid var(--gold);
  border-radius: 20px;
  padding: 15px 25px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.knowledge-subject-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
  border-color: #FFD700;
}

.show-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(80, 200, 120, 0.4);
}

/* Subject grid */
.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.subject-card {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subject-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 45px rgba(212, 175, 55, 0.3);
  border-color: var(--gold);
}

.subject-title {
  font-family: var(--font-heading);
  font-size: 1.3rem;
  color: var(--gold);
  margin-bottom: 1rem;
}

/* Difficulty selection */
.difficulty-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.difficulty-button {
  background: linear-gradient(45deg, var(--emerald), #90EE90);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 20px;
  font-family: var(--font-heading);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.difficulty-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(80, 200, 120, 0.4);
}

.difficulty-button.hard {
  background: linear-gradient(45deg, var(--burgundy), #DC143C);
}

.difficulty-button.intermediate {
  background: linear-gradient(45deg, #FF8C00, #FFA500);
}

/* Popup Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.popup-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border: 2px solid var(--gold);
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.popup-close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  color: var(--gold);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
}

.popup-title {
  color: var(--gold);
  font-family: var(--font-heading);
  font-size: 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
}

.popup-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.popup-button {
  padding: 1rem;
  border: none;
  border-radius: 10px;
  font-family: var(--font-heading);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.popup-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Magical Button */
.magical-button {
  background: linear-gradient(45deg, var(--gold), #FFD700);
  color: #000;
  border: 2px solid var(--gold);
  padding: 12px 25px;
  border-radius: 20px;
  font-family: var(--font-heading);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.magical-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.magical-button.selected {
  background: linear-gradient(45deg, #FFD700, var(--gold));
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.5);
}
