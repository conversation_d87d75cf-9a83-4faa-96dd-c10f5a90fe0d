# 🪄 Quizzcasso - Magical Quiz Experience

> *Where Knowledge Meets Magic - Embark on Your Enchanted Learning Journey*

A beautiful, interactive quiz platform built for the **IIT Gandhinagar Quizzing Society** with Harry <PERSON>-themed magical design and comprehensive learning features.



## 🛠️ Tech Stack

- **Frontend**: React with Vite
- **Styling**: Custom CSS with magical themes
- **Deployment**: Github pages


## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/SrushhDandekar/Quizzcasso.git
   cd Quizzcasso
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
npm run preview
```

## 📜 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build optimized production bundle |
| `npm run preview` | Preview production build locally |


## 🎯 Usage

1. **Home Page**: Welcome screen with magical animations
2. **Quiz Selection**: Choose from different categories and difficulty levels
3. **Quiz Taking**: Interactive questions with immediate feedback
4. **Leaderboard**: View top performers and your ranking
5. **About/Team**: Learn about the Quizzing Society and team members

## 🤝 Contributing

This project is maintained by the **Quizzing Society at IIT Gandhinagar**. 

For contributions:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 👨‍💻 Author

**Srushti Dandekar**  
*Club Secretary, Quizzing Society*  
*IIT Gandhinagar, BTech'23 Civil Engineering*

*"It is our choices, Harry, that show what we truly are, far more than our abilities."* - Albus Dumbledore

**Happy Quizzing! 🧙‍♂️✨**
