<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Background Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #1a1a2e;
            color: #e6e6e6;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                /* 1. Open Book - Sharp Corners */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Crect x='20' y='30' width='25' height='40'/%3E%3Crect x='55' y='30' width='25' height='40'/%3E%3Cpath d='M25 35h15M25 40h12M25 45h14M60 35h15M60 40h12M60 45h14'/%3E%3C/g%3E%3C/svg%3E"),
                /* 2. Stack of Books - Sharp Corners */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Crect x='30' y='20' width='40' height='8'/%3E%3Crect x='28' y='30' width='44' height='8'/%3E%3Crect x='32' y='40' width='36' height='8'/%3E%3Crect x='34' y='50' width='32' height='8'/%3E%3Crect x='36' y='60' width='28' height='8'/%3E%3C/g%3E%3C/svg%3E"),
                /* 3. Medal */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Ccircle cx='50' cy='50' r='20'/%3E%3Cpath d='M40 30l10-15 10 15'/%3E%3Cpath d='M50 40l2 4 4 1-3 3 1 4-4-2-4 2 1-4-3-3 4-1 2-4z'/%3E%3C/g%3E%3C/svg%3E"),
                /* 4. Trophy */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Crect x='35' y='25' width='30' height='25'/%3E%3Crect x='25' y='30' width='10' height='10'/%3E%3Crect x='65' y='30' width='10' height='10'/%3E%3Crect x='45' y='50' width='10' height='8'/%3E%3Crect x='35' y='58' width='30' height='4'/%3E%3C/g%3E%3C/svg%3E"),
                /* 5. Question Mark */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Cpath d='M50 25c-8 0-15 7-15 15h6c0-5 4-9 9-9s9 4 9 9c0 4-9 6-9 15h6c0-6 9-8 9-15 0-8-7-15-15-15z'/%3E%3Ccircle cx='50' cy='65' r='3'/%3E%3C/g%3E%3C/svg%3E"),
                /* 6. Brain */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Cpath d='M25 45c0-10 8-18 18-18 4 0 8 1 11 4 3-3 7-4 11-4 10 0 18 8 18 18 0 4-1 8-4 11 3 3 4 7 4 11 0 10-8 18-18 18-4 0-8-1-11-4-3 3-7 4-11 4-10 0-18-8-18-18 0-4 1-8 4-11-3-3-4-7-4-11z'/%3E%3Ccircle cx='70' cy='25' r='2'/%3E%3Ccircle cx='75' cy='20' r='1.5'/%3E%3C/g%3E%3C/svg%3E"),
                /* 7. Target */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Ccircle cx='50' cy='50' r='25'/%3E%3Ccircle cx='50' cy='50' r='18'/%3E%3Ccircle cx='50' cy='50' r='11'/%3E%3Ccircle cx='50' cy='50' r='4'/%3E%3C/g%3E%3C/svg%3E"),
                /* 8. Atom */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Ccircle cx='50' cy='50' r='4'/%3E%3Cellipse cx='50' cy='50' rx='25' ry='10'/%3E%3Cellipse cx='50' cy='50' rx='25' ry='10' transform='rotate(60 50 50)'/%3E%3Cellipse cx='50' cy='50' rx='25' ry='10' transform='rotate(120 50 50)'/%3E%3Ccircle cx='75' cy='50' r='2'/%3E%3Ccircle cx='37' cy='65' r='2'/%3E%3C/g%3E%3C/svg%3E"),
                /* 9. Calculator */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Crect x='30' y='20' width='40' height='55'/%3E%3Crect x='35' y='25' width='30' height='12'/%3E%3Ccircle cx='40' cy='45' r='3'/%3E%3Ccircle cx='50' cy='45' r='3'/%3E%3Ccircle cx='60' cy='45' r='3'/%3E%3Ccircle cx='40' cy='55' r='3'/%3E%3Ccircle cx='50' cy='55' r='3'/%3E%3Ccircle cx='60' cy='55' r='3'/%3E%3C/g%3E%3C/svg%3E"),
                /* 10. Graduation Cap */
                url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='white' stroke-width='1.5'%3E%3Cpath d='M20 45l30-10 30 10-30 10-30-10z'/%3E%3Cpath d='M50 55v15l-10-4v-11'/%3E%3Crect x='68' y='48' width='4' height='22'/%3E%3C/g%3E%3C/svg%3E");
            background-size: 
                80px 80px,    /* 1. Open Book */
                70px 70px,    /* 2. Stack of Books */
                60px 60px,    /* 3. Medal */
                90px 90px,    /* 4. Trophy */
                65px 65px,    /* 5. Question Mark */
                85px 85px,    /* 6. Brain */
                70px 70px,    /* 7. Target */
                85px 85px,    /* 8. Atom */
                55px 55px,    /* 9. Calculator */
                65px 65px;    /* 10. Graduation Cap */
            background-position:
                5% 5%,        /* 1. Open Book - Top Left */
                25% 5%,       /* 2. Stack of Books - Top */
                45% 5%,       /* 3. Medal - Top */
                65% 5%,       /* 4. Trophy - Top */
                85% 5%,       /* 5. Question Mark - Top Right */
                5% 35%,       /* 6. Brain - Middle Left */
                25% 35%,      /* 7. Target - Middle */
                45% 35%,      /* 8. Atom - Middle */
                65% 35%,      /* 9. Calculator - Middle */
                85% 35%;      /* 10. Graduation Cap - Middle Right */
            background-repeat: no-repeat;
            pointer-events: none;
            z-index: -3;
            opacity: 0.3;
        }

        .content {
            background-color: rgba(0, 0, 0, 0.5);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            max-width: 800px;
            z-index: 1;
        }

        h1 {
            color: gold;
            margin-bottom: 1rem;
        }

        p {
            line-height: 1.6;
            margin-bottom: 1rem;
            color: silver;
        }

        strong {
            color: gold;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>Quizzcasso Fixed Background</h1>
        <p>Professional background with 10 border-only knowledge-themed elements. Each icon uses only white borders with no fill colors, positioned strategically to avoid overlapping. Books feature sharp corners as requested.</p>
        <br><br>
        <p><strong>10 Border-Only Elements:</strong> Open Book (sharp corners), Stack of Books (sharp corners), Medal, Trophy, Question Mark, Brain, Target, Atom, Calculator, and Graduation Cap - all with clean white borders only, no fill colors, strategic non-overlapping positioning.</p>
    </div>
</body>
</html>
