// Question Database for Quizzcasso
// Add your questions here following the format below

export const academicQuestions = {
  // ENGLISH QUESTIONS
  english: {
    easy: [
      {
        id: 1,
        question: "What is the plural of 'child'?",
        options: ["childs", "children", "childes", "child"],
        correct: "children"
      },
      {
        id: 2,
        question: "Which word is a synonym for 'happy'?",
        options: ["sad", "angry", "joyful", "tired"],
        correct: "joyful"
      },
      {
        id: 3,
        question: "What is the past tense of 'run'?",
        options: ["runned", "ran", "running", "runs"],
        correct: "ran"
      },
      {
        id: 4,
        question: "Which is a proper noun?",
        options: ["city", "London", "building", "person"],
        correct: "London"
      },
      {
        id: 5,
        question: "What type of word is 'quickly'?",
        options: ["noun", "verb", "adjective", "adverb"],
        correct: "adverb"
      },
      {
        id: 6,
        question: "Which sentence is correct?",
        options: ["I have went to school", "I have gone to school", "I has gone to school", "I have go to school"],
        correct: "I have gone to school"
      },
      {
        id: 7,
        question: "What is the opposite of 'big'?",
        options: ["large", "huge", "small", "giant"],
        correct: "small"
      },
      {
        id: 8,
        question: "Which word rhymes with 'cat'?",
        options: ["dog", "hat", "car", "cup"],
        correct: "hat"
      },
      {
        id: 9,
        question: "What is a group of words that expresses a complete thought?",
        options: ["phrase", "sentence", "clause", "word"],
        correct: "sentence"
      },
      {
        id: 10,
        question: "Which is the correct spelling?",
        options: ["recieve", "receive", "receve", "receiv"],
        correct: "receive"
      },
      {
        id: 11,
        question: "What punctuation mark ends a question?",
        options: ["period", "comma", "question mark", "exclamation point"],
        correct: "question mark"
      },
      {
        id: 12,
        question: "Which word is a verb?",
        options: ["beautiful", "quickly", "jump", "happiness"],
        correct: "jump"
      }
    ],
    intermediate: [
      {
        id: 1,
        question: "What is a metaphor?",
        options: ["A direct comparison", "An indirect comparison", "A rhyme scheme", "A type of poem"],
        correct: "An indirect comparison"
      },
      // Add 29 more intermediate English questions here...
    ],
    hard: [
      {
        id: 1,
        question: "Who wrote 'Pride and Prejudice'?",
        options: ["Charlotte Bronte", "Jane Austen", "Emily Dickinson", "Virginia Woolf"],
        correct: "Jane Austen"
      },
      // Add 29 more hard English questions here...
    ]
  },

  // MATHEMATICS QUESTIONS
  maths: {
    easy: [
      {
        id: 1,
        question: "What is 15 + 27?",
        options: ["42", "41", "43", "40"],
        correct: "42"
      },
      {
        id: 2,
        question: "What is 8 × 7?",
        options: ["54", "56", "58", "52"],
        correct: "56"
      },
      {
        id: 3,
        question: "What is 100 ÷ 4?",
        options: ["20", "25", "30", "35"],
        correct: "25"
      },
      {
        id: 4,
        question: "What is 12²?",
        options: ["144", "124", "142", "146"],
        correct: "144"
      },
      {
        id: 5,
        question: "What is 50% of 80?",
        options: ["30", "35", "40", "45"],
        correct: "40"
      },
      {
        id: 6,
        question: "What is the square root of 64?",
        options: ["6", "7", "8", "9"],
        correct: "8"
      },
      {
        id: 7,
        question: "What is 3 + 4 × 2?",
        options: ["10", "11", "14", "16"],
        correct: "11"
      },
      {
        id: 8,
        question: "What is 15 - 8 + 3?",
        options: ["8", "9", "10", "11"],
        correct: "10"
      },
      {
        id: 9,
        question: "What is 2³?",
        options: ["6", "8", "9", "12"],
        correct: "8"
      },
      {
        id: 10,
        question: "What is 72 ÷ 9?",
        options: ["7", "8", "9", "10"],
        correct: "8"
      },
      {
        id: 11,
        question: "What is 25% of 200?",
        options: ["40", "45", "50", "55"],
        correct: "50"
      },
      {
        id: 12,
        question: "What is 6 × 9?",
        options: ["52", "54", "56", "58"],
        correct: "54"
      }
      {
        id: 2,
        question: "What is 8 × 7?",
        options: ["54", "56", "58", "52"],
        correct: "56"
      },
      // Add 28 more easy math questions here...
    ],
    intermediate: [
      {
        id: 1,
        question: "What is the square root of 144?",
        options: ["12", "14", "16", "10"],
        correct: "12"
      },
      // Add 29 more intermediate math questions here...
    ],
    hard: [
      {
        id: 1,
        question: "What is the derivative of x²?",
        options: ["x", "2x", "x²", "2x²"],
        correct: "2x"
      },
      // Add 29 more hard math questions here...
    ]
  },

  // BIOLOGY QUESTIONS
  biology: {
    easy: [
      {
        id: 1,
        question: "What is the powerhouse of the cell?",
        options: ["Nucleus", "Mitochondria", "Ribosome", "Cytoplasm"],
        correct: "Mitochondria"
      },
      {
        id: 2,
        question: "How many chambers does a human heart have?",
        options: ["2", "3", "4", "5"],
        correct: "4"
      },
      // Add 28 more easy biology questions here...
    ],
    intermediate: [
      {
        id: 1,
        question: "What is the process by which plants make food?",
        options: ["Respiration", "Photosynthesis", "Digestion", "Circulation"],
        correct: "Photosynthesis"
      },
      // Add 29 more intermediate biology questions here...
    ],
    hard: [
      {
        id: 1,
        question: "What is the scientific name for humans?",
        options: ["Homo erectus", "Homo sapiens", "Homo habilis", "Homo neanderthalensis"],
        correct: "Homo sapiens"
      },
      // Add 29 more hard biology questions here...
    ]
  },

  // PHYSICS QUESTIONS
  physics: {
    easy: [
      {
        id: 1,
        question: "What is the speed of light?",
        options: ["300,000 km/s", "3,000,000 km/s", "30,000 km/s", "300,000,000 m/s"],
        correct: "300,000,000 m/s"
      },
      // Add 29 more easy physics questions here...
    ],
    intermediate: [
      {
        id: 1,
        question: "What is Newton's first law of motion?",
        options: ["F=ma", "Law of inertia", "Action-reaction", "Conservation of energy"],
        correct: "Law of inertia"
      },
      // Add 29 more intermediate physics questions here...
    ],
    hard: [
      {
        id: 1,
        question: "What is Planck's constant approximately?",
        options: ["6.626 × 10⁻³⁴ J·s", "9.109 × 10⁻³¹ kg", "1.602 × 10⁻¹⁹ C", "6.022 × 10²³ mol⁻¹"],
        correct: "6.626 × 10⁻³⁴ J·s"
      },
      // Add 29 more hard physics questions here...
    ]
  },

  // CHEMISTRY QUESTIONS
  chemistry: {
    easy: [
      {
        id: 1,
        question: "What is the chemical symbol for water?",
        options: ["H2O", "CO2", "NaCl", "O2"],
        correct: "H2O"
      },
      // Add 29 more easy chemistry questions here...
    ],
    intermediate: [
      {
        id: 1,
        question: "What is the pH of pure water?",
        options: ["6", "7", "8", "9"],
        correct: "7"
      },
      // Add 29 more intermediate chemistry questions here...
    ],
    hard: [
      {
        id: 1,
        question: "What is Avogadro's number?",
        options: ["6.022 × 10²³", "3.14159", "2.718", "1.602 × 10⁻¹⁹"],
        correct: "6.022 × 10²³"
      },
      // Add 29 more hard chemistry questions here...
    ]
  },

  // Continue this pattern for all subjects:
  // astrology, economics, history, geography, 'current-affairs', 
  // 'computer-knowledge', 'computer-languages', 'general-knowledge', 'all-in-one'
}

export const funQuestions = {
  'harry-potter': {
    beginner: [
      {
        id: 1,
        question: "What is Harry Potter's middle name?",
        options: ["James", "Sirius", "Remus", "Albus"],
        correct: "James"
      },
      {
        id: 2,
        question: "What house is Luna Lovegood in?",
        options: ["Gryffindor", "Hufflepuff", "Ravenclaw", "Slytherin"],
        correct: "Ravenclaw"
      },
      {
        id: 3,
        question: "What is the name of Harry's owl?",
        options: ["Hedwig", "Errol", "Pigwidgeon", "Crookshanks"],
        correct: "Hedwig"
      },
      {
        id: 4,
        question: "Who is the headmaster of Hogwarts?",
        options: ["Severus Snape", "Minerva McGonagall", "Albus Dumbledore", "Rubeus Hagrid"],
        correct: "Albus Dumbledore"
      },
      {
        id: 5,
        question: "What platform do students use to board the Hogwarts Express?",
        options: ["Platform 9", "Platform 9¾", "Platform 10", "Platform 8½"],
        correct: "Platform 9¾"
      },
      {
        id: 6,
        question: "What is Hermione's cat's name?",
        options: ["Hedwig", "Scabbers", "Crookshanks", "Mrs. Norris"],
        correct: "Crookshanks"
      },
      {
        id: 7,
        question: "What spell is used to disarm an opponent?",
        options: ["Expelliarmus", "Stupefy", "Protego", "Lumos"],
        correct: "Expelliarmus"
      },
      {
        id: 8,
        question: "What is the name of the three-headed dog?",
        options: ["Fluffy", "Fang", "Aragog", "Buckbeak"],
        correct: "Fluffy"
      },
      {
        id: 9,
        question: "What house is Draco Malfoy in?",
        options: ["Gryffindor", "Hufflepuff", "Ravenclaw", "Slytherin"],
        correct: "Slytherin"
      },
      {
        id: 10,
        question: "What is the name of the Weasley family home?",
        options: ["The Burrow", "Grimmauld Place", "Godric's Hollow", "Shell Cottage"],
        correct: "The Burrow"
      },
      {
        id: 11,
        question: "What does the spell 'Lumos' do?",
        options: ["Creates light", "Unlocks doors", "Levitates objects", "Heals wounds"],
        correct: "Creates light"
      },
      {
        id: 12,
        question: "Who teaches Potions at Hogwarts?",
        options: ["Severus Snape", "Minerva McGonagall", "Filius Flitwick", "Pomona Sprout"],
        correct: "Severus Snape"
      }
    ],
    middleman: [
      {
        id: 1,
        question: "What is the name of Hagrid's dragon?",
        options: ["Norbert", "Charlie", "Fluffy", "Aragog"],
        correct: "Norbert"
      },
      // Add 59 more Harry Potter middleman questions here...
    ],
    pro: [
      {
        id: 1,
        question: "What is the incantation for the Patronus charm?",
        options: ["Expecto Patronum", "Expelliarmus", "Protego", "Stupefy"],
        correct: "Expecto Patronum"
      },
      // Add 59 more Harry Potter pro questions here...
    ]
  },

  'marvel-avengers': {
    beginner: [
      {
        id: 1,
        question: "What is Spider-Man's real name?",
        options: ["Peter Parker", "Tony Stark", "Steve Rogers", "Bruce Banner"],
        correct: "Peter Parker"
      },
      // Add 59 more Marvel beginner questions here...
    ],
    middleman: [
      {
        id: 1,
        question: "What is the name of Thor's home planet?",
        options: ["Asgard", "Midgard", "Jotunheim", "Alfheim"],
        correct: "Asgard"
      },
      // Add 59 more Marvel middleman questions here...
    ],
    pro: [
      {
        id: 1,
        question: "What is the real name of the Winter Soldier?",
        options: ["Bucky Barnes", "Sam Wilson", "Steve Rogers", "Clint Barton"],
        correct: "Bucky Barnes"
      },
      // Add 59 more Marvel pro questions here...
    ]
  }
}

// Helper function to get random questions
export const getRandomQuestions = (subject, difficulty, count = 10) => {
  const questions = academicQuestions[subject]?.[difficulty] || []
  const shuffled = [...questions].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

export const getRandomFunQuestions = (category, level, count = 10) => {
  const questions = funQuestions[category]?.[level] || []
  const shuffled = [...questions].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}
